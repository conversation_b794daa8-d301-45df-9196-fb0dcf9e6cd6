@echo off
title SwingTrader AI - Quick Start
cls

echo SwingTrader AI - Quick Start
echo =============================
echo.

REM Check if Node.js is available
echo Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org/
    echo Then run this script again
    pause
    exit /b 1
)

echo Node.js found: 
node --version

echo.
echo Installing dependencies...
call npm install --silent

echo.
echo Starting SwingTrader AI...
echo.
echo Application will open at: http://localhost:3000
echo Press Ctrl+C to stop
echo.

REM Wait 3 seconds then open browser
timeout /t 3 >nul
start http://localhost:3000

REM Start the application
call npm start

pause
