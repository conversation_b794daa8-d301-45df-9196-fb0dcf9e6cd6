@echo off
title SwingTrader AI - Simple Installation
cls

echo ================================================================================
echo.
echo                SwingTrader AI - SIMPLE INSTALLATION
echo.
echo                    Professional AI-Powered Trading Platform
echo.
echo ================================================================================
echo.
echo This installer will set up SwingTrader AI with all API keys pre-configured.
echo No additional setup required - everything is ready to use!
echo.
echo Press any key to begin installation...
pause >nul

echo.
echo ================================================================================
echo STEP 1: Checking Node.js...
echo ================================================================================

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo Node.js is not installed. Please install Node.js first:
    echo 1. Go to https://nodejs.org/
    echo 2. Download and install the LTS version
    echo 3. Restart your computer
    echo 4. Run this installer again
    echo.
    echo Opening Node.js download page...
    start https://nodejs.org/
    pause
    exit /b 1
) else (
    echo SUCCESS: Node.js is installed
    node --version
    npm --version
)

echo.
echo ================================================================================
echo STEP 2: Installing Dependencies...
echo ================================================================================
echo This may take 2-3 minutes...
echo.

call npm install
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)

echo SUCCESS: All dependencies installed

echo.
echo ================================================================================
echo STEP 3: Setting up API Configuration...
echo ================================================================================

REM Create .env.local file with pre-configured API keys
if not exist ".env.local" (
    echo Creating API configuration file...
    (
        echo # SwingTrader AI - Pre-configured API Keys
        echo # All keys are ready to use - no setup required!
        echo.
        echo # Market Data Provider
        echo POLYGON_API_KEY=********************************
        echo.
        echo # AI Analysis Provider  
        echo OPENAI_API_KEY=********************************************************************************************************************************************************************
        echo.
        echo # Financial Data Provider
        echo FMP_API_KEY=K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7
        echo.
        echo # Paper Trading Platform
        echo ALPACA_API_KEY=PKKKYLNNZZT2EI7F3CVL
        echo ALPACA_SECRET_KEY=Bgh3CLNSueS9Odyeb6U38UddNEluGDSIflunjinD
        echo ALPACA_BASE_URL=https://paper-api.alpaca.markets
        echo.
        echo # Application Settings
        echo NEXT_PUBLIC_APP_URL=http://localhost:3000
    ) > .env.local
    echo SUCCESS: API configuration created
) else (
    echo SUCCESS: API configuration already exists
)

echo.
echo ================================================================================
echo STEP 4: Building Application...
echo ================================================================================
echo Optimizing for performance... This may take 1-2 minutes...
echo.

call npm run build
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Build failed
    echo This usually means there's an issue with the API keys or dependencies
    pause
    exit /b 1
)

echo SUCCESS: Application built successfully

echo.
echo ================================================================================
echo STEP 5: Creating Launch Scripts...
echo ================================================================================

REM Create simple start script
echo @echo off > "START-SWINGTRADER.bat"
echo title SwingTrader AI - Professional Trading Platform >> "START-SWINGTRADER.bat"
echo cls >> "START-SWINGTRADER.bat"
echo echo. >> "START-SWINGTRADER.bat"
echo echo SwingTrader AI is starting... >> "START-SWINGTRADER.bat"
echo echo. >> "START-SWINGTRADER.bat"
echo echo Application will open at: http://localhost:3000 >> "START-SWINGTRADER.bat"
echo echo Press Ctrl+C to stop the server >> "START-SWINGTRADER.bat"
echo echo. >> "START-SWINGTRADER.bat"
echo timeout /t 3 ^>nul >> "START-SWINGTRADER.bat"
echo start http://localhost:3000 >> "START-SWINGTRADER.bat"
echo call npm start >> "START-SWINGTRADER.bat"

echo SUCCESS: Launch script created

echo.
echo ================================================================================
echo                        INSTALLATION COMPLETE!
echo ================================================================================
echo.
echo SwingTrader AI is now ready to use!
echo.
echo TO START TRADING:
echo   Double-click: START-SWINGTRADER.bat
echo   Or run: npm start
echo.
echo FEATURES READY:
echo   * AI-Powered Trade Analysis
echo   * Real-time Market Scanning (65+ stocks)
echo   * Professional Trading Cards Interface
echo   * Risk Management Tools
echo   * Paper Trading System
echo   * Multiple Trading Strategies
echo.
echo ALL API KEYS ARE PRE-CONFIGURED - NO SETUP REQUIRED!
echo.
echo Would you like to launch SwingTrader AI now? (Y/N)
set /p LAUNCH_NOW=
if /i "!LAUNCH_NOW!"=="Y" (
    echo.
    echo Launching SwingTrader AI...
    timeout /t 2 >nul
    start "" "START-SWINGTRADER.bat"
) else (
    echo.
    echo You can launch SwingTrader AI anytime by double-clicking:
    echo START-SWINGTRADER.bat
)

echo.
echo Thank you for choosing SwingTrader AI!
echo Happy trading!
echo.
pause

exit /b 0
