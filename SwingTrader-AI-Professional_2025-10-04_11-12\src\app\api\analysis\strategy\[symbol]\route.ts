import { NextRequest, NextResponse } from 'next/server'
import { SwingTradingStrategies } from '@/lib/swingStrategies'
import { PolygonAPI } from '@/lib/polygon'
import { FMPAPI } from '@/lib/fmp'
import { format, subDays } from 'date-fns'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ symbol: string }> }
) {
  try {
    const { symbol } = await params
    const { searchParams } = new URL(request.url)
    
    const accountSize = parseInt(searchParams.get('accountSize') || '100000')
    const strategy = searchParams.get('strategy') // 'overnight', 'breakout', or 'both'
    
    if (!symbol) {
      return NextResponse.json(
        { error: 'Symbol parameter is required' },
        { status: 400 }
      )
    }

    // Get stock data
    const fmpAPI = new FMPAPI(process.env.FMP_API_KEY)
    const polygonAPI = new PolygonAPI(process.env.POLYGON_API_KEY)
    
    const [quote, historicalData] = await Promise.all([
      fmpAPI.getStockQuote(symbol.toUpperCase()),
      getHistoricalData(polygonAPI, symbol.toUpperCase())
    ])

    if (!historicalData || historicalData.length < 30) {
      return NextResponse.json(
        { error: 'Insufficient historical data for strategy analysis - need at least 30 days' },
        { status: 400 }
      )
    }

    // Analyze strategies
    const result: any = {
      symbol: symbol.toUpperCase(),
      quote,
      accountSize,
      scanTime: new Date().toISOString()
    }

    if (!strategy || strategy === 'overnight' || strategy === 'both') {
      result.overnightSetup = SwingTradingStrategies.analyzeOvernightMomentum(
        symbol.toUpperCase(), historicalData, quote, accountSize
      )
    }

    if (!strategy || strategy === 'breakout' || strategy === 'both') {
      result.breakoutSetup = SwingTradingStrategies.analyzeTechnicalBreakout(
        symbol.toUpperCase(), historicalData, quote, accountSize
      )
    }

    // Determine best strategy if both analyzed
    if (result.overnightSetup && result.breakoutSetup) {
      result.bestStrategy = result.overnightSetup.confidence > result.breakoutSetup.confidence 
        ? 'overnight_momentum' 
        : 'technical_breakout'
      result.overallScore = Math.max(result.overnightSetup.confidence, result.breakoutSetup.confidence) + 5
    } else if (result.overnightSetup) {
      result.bestStrategy = 'overnight_momentum'
      result.overallScore = result.overnightSetup.confidence
    } else if (result.breakoutSetup) {
      result.bestStrategy = 'technical_breakout'
      result.overallScore = result.breakoutSetup.confidence
    } else {
      result.overallScore = 0
      result.message = 'No valid swing trading setups found for current market conditions'
    }
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in strategy analysis API:', error)
    return NextResponse.json(
      { error: 'Failed to perform strategy analysis' },
      { status: 500 }
    )
  }
}

async function getHistoricalData(polygonAPI: PolygonAPI, symbol: string) {
  const to = format(new Date(), 'yyyy-MM-dd')
  const from = format(subDays(new Date(), 100), 'yyyy-MM-dd')
  
  try {
    return await polygonAPI.getHistoricalData(symbol, 'day', 1, from, to)
  } catch (error) {
    console.warn(`Historical data failed for ${symbol}`)
    throw error
  }
}
