# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                                                                              ║
# ║                    🚀 SwingTrader AI - API Configuration                     ║
# ║                                                                              ║
# ║                     Professional Trading Platform Setup                     ║
# ║                                                                              ║
# ╚══════════════════════════════════════════════════════════════════════════════╝
#
# IMPORTANT: Replace the placeholder values below with your actual API keys
#
# 🔒 SECURITY NOTE: Never share this file or commit it to version control
#                   Keep your API keys private and secure
#
# ═══════════════════════════════════════════════════════════════════════════════

# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                          📊 MARKET DATA PROVIDERS                          │
# └─────────────────────────────────────────────────────────────────────────────┘

# 🔹 POLYGON.IO - Primary Market Data Provider (REQUIRED)
# ────────────────────────────────────────────────────────────────────────────
# Purpose: Real-time and historical stock data, technical indicators
# Sign up: https://polygon.io/
# Plan: Free tier available (limited requests), Paid plans for more data
# How to get:
#   1. Visit https://polygon.io/
#   2. Create free account
#   3. Verify email
#   4. Go to Dashboard → API Keys
#   5. Copy your API key
POLYGON_API_KEY=your_polygon_api_key_here

# 🔹 FINANCIAL MODELING PREP - Additional Financial Data (OPTIONAL)
# ────────────────────────────────────────────────────────────────────────────
# Purpose: Company financials, analyst ratings, earnings data
# Sign up: https://financialmodelingprep.com/
# Plan: Free tier available (250 requests/day), Paid plans for more
# How to get:
#   1. Visit https://financialmodelingprep.com/
#   2. Create free account
#   3. Verify email
#   4. Go to Dashboard → API
#   5. Copy your API key
FMP_API_KEY=your_fmp_api_key_here

# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                           🤖 AI ANALYSIS PROVIDER                          │
# └─────────────────────────────────────────────────────────────────────────────┘

# 🔹 OPENAI - AI-Powered Trade Analysis (REQUIRED for AI features)
# ────────────────────────────────────────────────────────────────────────────
# Purpose: Intelligent trade analysis, market insights, risk assessment
# Sign up: https://platform.openai.com/
# Plan: Pay-per-use (typically $0.01-0.03 per analysis)
# How to get:
#   1. Visit https://platform.openai.com/
#   2. Create account
#   3. Add billing method (required for API access)
#   4. Go to API Keys section
#   5. Create new secret key
#   6. Copy the key (you won't see it again!)
OPENAI_API_KEY=your_openai_api_key_here

# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                          📈 TRADING PLATFORMS                              │
# └─────────────────────────────────────────────────────────────────────────────┘

# 🔹 ALPACA TRADING - Paper Trading & Live Trading (OPTIONAL)
# ────────────────────────────────────────────────────────────────────────────
# Purpose: Execute paper trades, track P&L, eventually live trading
# Sign up: https://alpaca.markets/
# Plan: Free paper trading, Live trading requires account funding
# How to get:
#   1. Visit https://alpaca.markets/
#   2. Create account
#   3. Complete verification
#   4. Go to Paper Trading → API Keys
#   5. Generate new API key pair
ALPACA_API_KEY=your_alpaca_api_key_here
ALPACA_SECRET_KEY=your_alpaca_secret_key_here
ALPACA_BASE_URL=https://paper-api.alpaca.markets

# ┌─────────────────────────────────────────────────────────────────────────────┐
# │                          ⚙️ APPLICATION SETTINGS                           │
# └─────────────────────────────────────────────────────────────────────────────┘

# Application URL (change if running on different port)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Development mode (set to 'production' for live deployment)
NODE_ENV=development

# ═══════════════════════════════════════════════════════════════════════════════
#
# 🎯 QUICK START CHECKLIST:
# ═══════════════════════════════════════════════════════════════════════════════
#
# ✅ MINIMUM REQUIRED (for basic functionality):
#    □ POLYGON_API_KEY - Get free key from polygon.io
#    □ OPENAI_API_KEY - Get key from platform.openai.com (requires billing)
#
# ✅ RECOMMENDED (for full features):
#    □ FMP_API_KEY - Additional financial data
#    □ ALPACA keys - Paper trading functionality
#
# ✅ OPTIONAL (for advanced features):
#    □ Additional data providers can be added later
#
# ═══════════════════════════════════════════════════════════════════════════════
#
# 💡 COST ESTIMATES (Monthly):
# ═══════════════════════════════════════════════════════════════════════════════
#
# 🆓 FREE TIER USAGE:
#    • Polygon.io: Free (5 requests/minute)
#    • FMP: Free (250 requests/day)
#    • OpenAI: ~$5-15/month (depending on usage)
#    • Alpaca: Free paper trading
#
# 💰 PROFESSIONAL USAGE:
#    • Polygon.io: $99/month (unlimited requests)
#    • FMP: $15/month (10,000 requests/day)
#    • OpenAI: ~$20-50/month (heavy usage)
#    • Alpaca: Free paper trading, live trading fees apply
#
# ═══════════════════════════════════════════════════════════════════════════════
#
# 🔧 TROUBLESHOOTING:
# ═══════════════════════════════════════════════════════════════════════════════
#
# ❌ "API key invalid" errors:
#    • Double-check you copied the full key
#    • Ensure no extra spaces or quotes
#    • Verify the key is active in your provider dashboard
#
# ❌ "Rate limit exceeded" errors:
#    • You've hit your plan's request limit
#    • Wait for reset or upgrade your plan
#    • Check your usage in provider dashboard
#
# ❌ "Insufficient credits" (OpenAI):
#    • Add billing method to your OpenAI account
#    • Add credits to your account balance
#
# ═══════════════════════════════════════════════════════════════════════════════
