import { <PERSON><PERSON><PERSON>, EventName, Contract } from '@stoqey/ib'
import { StockData } from '@/types/trading'

export interface IBKRConfig {
  host: string
  port: number
  clientId: number
  paperTrading: boolean
}

export interface IBKRMarketData {
  symbol: string
  price: number
  change: number
  changePercent: number
  volume: number
  bid: number
  ask: number
  last: number
  close: number
  previousClose: number
  marketCap?: number
  avgVolume?: number
}

export class IBKRAPI {
  private ib: IBApi
  private config: IBKRConfig
  private connected: boolean = false
  private marketDataRequests: Map<number, { symbol: string; resolve: Function; reject: Function }> = new Map()
  private nextReqId: number = 1

  constructor(config: IBKRConfig) {
    this.config = config
    this.ib = new IBApi({
      host: config.host,
      port: config.port,
      clientId: config.clientId,
    })

    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    // Connection events
    this.ib.on(EventName.connected, () => {
      console.log('✅ Connected to IBKR TWS')
      this.connected = true
    })

    this.ib.on(EventName.disconnected, () => {
      console.log('❌ Disconnected from IBKR TWS')
      this.connected = false
    })

    this.ib.on(EventName.error, (err, code, reqId) => {
      console.error(`IBKR Error ${code}:`, err)
      
      // Handle market data request errors
      if (reqId && this.marketDataRequests.has(reqId)) {
        const request = this.marketDataRequests.get(reqId)!
        request.reject(new Error(`IBKR Error ${code}: ${err}`))
        this.marketDataRequests.delete(reqId)
      }
    })

    // Market data events
    this.ib.on(EventName.tickPrice, (reqId: number, tickType: number, price: number, canAutoExecute: number) => {
      const request = this.marketDataRequests.get(reqId)
      if (request) {
        // Store price data - we'll collect multiple ticks before resolving
        if (!request.data) request.data = {}
        
        switch (tickType) {
          case 1: // Bid
            request.data.bid = price
            break
          case 2: // Ask
            request.data.ask = price
            break
          case 4: // Last
            request.data.last = price
            request.data.price = price
            break
          case 9: // Close
            request.data.close = price
            request.data.previousClose = price
            break
        }
      }
    })

    this.ib.on(EventName.tickSize, (reqId: number, tickType: number, size: number) => {
      const request = this.marketDataRequests.get(reqId)
      if (request) {
        if (!request.data) request.data = {}
        
        switch (tickType) {
          case 0: // Bid size
            request.data.bidSize = size
            break
          case 3: // Ask size
            request.data.askSize = size
            break
          case 5: // Last size
            request.data.lastSize = size
            break
          case 8: // Volume
            request.data.volume = size
            break
        }
      }
    })

    // Market data snapshot complete
    this.ib.on(EventName.tickSnapshotEnd, (reqId: number) => {
      const request = this.marketDataRequests.get(reqId)
      if (request) {
        const data = request.data || {}
        const marketData: IBKRMarketData = {
          symbol: request.symbol,
          price: data.last || data.close || data.bid || data.ask || 0,
          change: data.last && data.previousClose ? data.last - data.previousClose : 0,
          changePercent: data.last && data.previousClose ? ((data.last - data.previousClose) / data.previousClose) * 100 : 0,
          volume: data.volume || 0,
          bid: data.bid || 0,
          ask: data.ask || 0,
          last: data.last || 0,
          close: data.close || 0,
          previousClose: data.previousClose || 0,
          marketCap: 0,
          avgVolume: 0
        }
        
        request.resolve(marketData)
        this.marketDataRequests.delete(reqId)
      }
    })
  }

  async connect(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (this.connected) {
        resolve(true)
        return
      }

      const timeout = setTimeout(() => {
        reject(new Error('IBKR connection timeout - ensure TWS/IB Gateway is running'))
      }, 10000)

      this.ib.once(EventName.connected, () => {
        clearTimeout(timeout)
        resolve(true)
      })

      this.ib.once(EventName.error, (err) => {
        clearTimeout(timeout)
        reject(new Error(`IBKR connection failed: ${err}`))
      })

      console.log(`🔌 Connecting to IBKR TWS at ${this.config.host}:${this.config.port}...`)
      this.ib.connect()
    })
  }

  disconnect(): void {
    if (this.connected) {
      this.ib.disconnect()
    }
  }

  private createStockContract(symbol: string): Contract {
    return {
      symbol: symbol.toUpperCase(),
      secType: 'STK',
      exchange: 'SMART',
      currency: 'USD',
    }
  }

  async getMarketData(symbol: string): Promise<IBKRMarketData> {
    if (!this.connected) {
      throw new Error('Not connected to IBKR - ensure TWS/IB Gateway is running')
    }

    return new Promise((resolve, reject) => {
      const reqId = this.nextReqId++
      const contract = this.createStockContract(symbol)

      // Store the request
      this.marketDataRequests.set(reqId, {
        symbol: symbol.toUpperCase(),
        resolve,
        reject,
        data: {}
      })

      // Set timeout for the request
      setTimeout(() => {
        if (this.marketDataRequests.has(reqId)) {
          this.marketDataRequests.delete(reqId)
          reject(new Error(`Market data request timeout for ${symbol}`))
        }
      }, 10000)

      // Request market data snapshot
      this.ib.reqMktData(reqId, contract, '', true, false, [])
    })
  }

  // Convert IBKR market data to StockData format
  async getStockQuote(symbol: string): Promise<StockData> {
    const marketData = await this.getMarketData(symbol)
    
    return {
      symbol: marketData.symbol,
      name: marketData.symbol, // IBKR doesn't provide company names in market data
      price: marketData.price,
      change: marketData.change,
      changePercent: marketData.changePercent,
      volume: marketData.volume,
      marketCap: marketData.marketCap,
      pe: undefined, // Not available in basic market data
      dividend: undefined // Not available in basic market data
    }
  }

  isConnected(): boolean {
    return this.connected
  }
}
